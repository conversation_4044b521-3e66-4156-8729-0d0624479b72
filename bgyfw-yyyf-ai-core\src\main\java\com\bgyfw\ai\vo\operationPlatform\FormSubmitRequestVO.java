package com.bgyfw.ai.vo.operationPlatform;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * 表单提交请求参数VO
 *
 * <AUTHOR>
 * @date 2025/5/22 10:24
 */

/**
 * 表单提交请求参数VO
 */
@Data
public class FormSubmitRequestVO {


    @Schema(description = "服务级别协议ID")
    private String serviceLevelAgreementId;

    @Schema(description = "服务分类", example = "GENERIC_SERVICE", required = true)
    private String category;

    @Schema(description = "部门ID", example = "f4319fa3-e9a2-4ff6-92e1-21cce6f5420d", required = true)
    private String businessGroupId;

    @Schema(description = "项目ID", example = "06b38195-3728-4523-a9b5-1331aafaa1f2", required = true)
    private String projectId;

    @Schema(description = "工单名称", example = "测试api创建2", required = true)
    private String name;

    @Schema(description = "工单描述", example = "测试api创建2")
    private String description;

    @Schema(description = "处理流程ID（固定值）", example = "25eaa111-6634-4858-acc7-6cbcf1d4439d", required = true)
    private String processDefinitionId;

    /**
     * 导数任务流水线id【固定值】
     */
    @Schema(description = "导数任务流水线id【固定值】", example = "184f3909-c296-449a-b41d-b8fa97d97a79", required = true)
    private String pipelineConfigId = "184f3909-c296-449a-b41d-b8fa97d97a79";

    @Schema(description = "流程表单数据", required = true)
    private ProcessForm processForm;

    @Schema(description = "申请人账户ID", example = "d29f5ddb-8c40-4004-8842-65522b69e9a1", required = true)
    private String requestUserId;

    @Schema(description = "负责人ID", example = "d29f5ddb-8c40-4004-8842-65522b69e9a1", required = true)
    private String ownerId;

    @Schema(description = "申请人名称", example = "xietong_user", required = true)
    private String applicant;

    @Schema(description = "请求参数配置", required = true)
    private RequestParameters requestParameters;


}
