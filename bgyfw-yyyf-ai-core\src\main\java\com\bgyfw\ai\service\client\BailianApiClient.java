package com.bgyfw.ai.service.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.bgyfw.ai.constant.BaseBailianProperties;
import com.bgyfw.ai.exception.ServiceException;
import com.bgyfw.ai.vo.bailian.BailianChatRequestVO;
import com.bgyfw.ai.vo.bailian.BailianChatResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 百炼 API 客户端
 *
 * <AUTHOR>
 * @date 2025/8/18
 */
@Component
@Slf4j
public class BailianApiClient {

    @Autowired
    private BaseBailianProperties bailianProperties;

    /**
     * 发送聊天消息
     *
     * @param appId 应用 ID
     * @param token API Token
     * @param prompt 用户输入的提示词
     * @return 聊天响应
     * @throws Exception 异常
     */
    public BailianChatResponseVO chat(String appId, String token, String prompt) throws Exception {
        return chat(appId, token, prompt, null, null, null);
    }

    /**
     * 发送聊天消息
     *
     * @param appId 应用 ID
     * @param token API Token
     * @param prompt 用户输入的提示词
     * @param sessionId 会话 ID（可选）
     * @return 聊天响应
     * @throws Exception 异常
     */
    public BailianChatResponseVO chat(String appId, String token, String prompt, String sessionId) throws Exception {
        return chat(appId, token, prompt, sessionId, null, null);
    }

    /**
     * 发送聊天消息
     *
     * @param appId 应用 ID
     * @param token API Token
     * @param prompt 用户输入的提示词
     * @param sessionId 会话 ID（可选）
     * @param stream 是否流式输出（可选）
     * @param parameters 额外参数（可选）
     * @return 聊天响应
     * @throws Exception 异常
     */
    public BailianChatResponseVO chat(String appId, String token, String prompt, String sessionId, 
                                     Boolean stream, Map<String, Object> parameters) throws Exception {
        
        // 参数校验
        if (StrUtil.isBlank(appId)) {
            appId = bailianProperties.getDefaultAppId();
        }
        if (StrUtil.isBlank(token)) {
            token = bailianProperties.getDefaultToken();
        }
        if (StrUtil.isBlank(appId)) {
            throw new IllegalArgumentException("appId 不能为空");
        }
        if (StrUtil.isBlank(token)) {
            throw new IllegalArgumentException("token 不能为空");
        }
        if (StrUtil.isBlank(prompt)) {
            throw new IllegalArgumentException("prompt 不能为空");
        }

        // 构建请求对象
        BailianChatRequestVO requestVO = new BailianChatRequestVO();
        BailianChatRequestVO.Input input = new BailianChatRequestVO.Input();
        input.setPrompt(prompt);
        input.setSession_id(sessionId);
        input.setStream(stream != null ? stream : false);
        requestVO.setInput(input);
        
        // 设置额外参数，如果没有传入则使用默认参数
        if (parameters == null) {
            parameters = Map.of("flow_stream_mode", "full_thoughts");
        }
        requestVO.setParameters(parameters);

        // 构建请求 URL
        String url = bailianProperties.getBaseUrl() + "/" + appId + "/completion";
        
        // 发送请求
        String requestBody = JSONUtil.toJsonStr(requestVO);

        HttpResponse response = HttpRequest.post(url)
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", "application/json")
                .timeout(bailianProperties.getTimeout())
                .setConnectionTimeout(bailianProperties.getConnectTimeout())
                .setReadTimeout(bailianProperties.getReadTimeout())
                .body(requestBody)
                .execute();

        String responseBody = response.body();
        log.info("百炼 API 请求 URL: {}, 请求参数: {}, 响应结果: {}", url, requestBody, responseBody);

        if (response.getStatus() != 200) {
            throw new ServiceException("百炼 API 调用失败，状态码：" + response.getStatus() + "，响应：" + responseBody);
        }

        // 解析响应
        return JSONUtil.toBean(responseBody, BailianChatResponseVO.class);
    }

    /**
     * 使用默认配置发送聊天消息
     *
     * @param prompt 用户输入的提示词
     * @return 聊天响应
     * @throws Exception 异常
     */
    public BailianChatResponseVO chatWithDefault(String prompt) throws Exception {
        return chat(null, null, prompt);
    }

    /**
     * 使用默认配置发送聊天消息（带会话 ID）
     *
     * @param prompt 用户输入的提示词
     * @param sessionId 会话 ID
     * @return 聊天响应
     * @throws Exception 异常
     */
    public BailianChatResponseVO chatWithDefault(String prompt, String sessionId) throws Exception {
        return chat(null, null, prompt, sessionId);
    }
}
