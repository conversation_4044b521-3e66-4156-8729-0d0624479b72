package com.bgyfw.ai.vo.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:41
 */
@Data
public class DifyUploadFileResponse {

    private String code;
    private String message;
    private String id;
    private String name;
    private long size;
    private String extension;

    @JsonProperty("mime_type")
    private String mimeType;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("created_at")
    private long createdAt;

    @JsonProperty("preview_url")
    private String previewUrl;
}
