package com.bgyfw.ai.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;

/**
 * 基本异常，系统定义的所有异常都需要继承这个基本类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 所属模块
     */
    private String module;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误码对应的参数
     */
    private Object[] args;


    /**
     * 错误消息
     */
    private String message;

    public BaseException(String module, Integer code, Object[] args, String message) {
        this.module = module;
        this.code = code;
        this.args = args;
        this.message =message;
    }


    public BaseException(String module, Integer code, Object[] args) {
        this.module = module;
        this.code = code;
        this.args = args;
    }


    public BaseException(String module, Integer code, String message) {
        this.module = module;
        this.code = code;
        this.message = message;
    }

    public BaseException(String module, String message) {
        this.module = module;
        this.message = message;
    }

    public BaseException(String module, Integer code) {
        this.module = module;
        this.code = code;
    }

    public BaseException(Integer code, Object[] args) {
        this.code = code;
        this.args = args;
    }

    public BaseException(Object[] args, String message) {
        this.args = args;
        this.message = message;
    }

    public BaseException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public BaseException(String message) {
        super(message);
        this.message = message;
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }

    protected BaseException(String message, Throwable cause, boolean enableSuppression,
                            boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.message = message;
    }

    @Override
    public String toString() {
        return this.getClass().getSimpleName() + "{" +
                "module='" + module + '\'' +
                ", code=" + code +
                ", args=" + Arrays.toString(args) +
                ", message='" + message + '\'' +
                '}';
    }
}
