package com.bgyfw.ai.vo.operationPlatform;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * 文件上传响应结果VO
 *
 * <AUTHOR>
 * @date 2025/5/22 10:31
 */

/**
 * 文件上传响应结果VO
 */

@Data
public class FileUploadResponseVO {
    @Schema(description = "文件名", example = "ncc_data.sql")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "88")
    private Integer size;

    @Schema(description = "文件状态", example = "done")
    private String status;

    @Schema(description = "文件唯一ID", example = "d5b1f9b0-51b7-41f6-8e79-d1c1e4e21081")
    private String id;

    @Schema(description = "文件唯一标识", example = "6lizegj5we9")
    private String uid;
}
