package com.bgyfw.ai.vo.operationPlatform;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * 表单提交请求参数VO
 *
 * <AUTHOR>
 * @date 2025/5/22 10:24
 */

/**
 * 表单提交请求参数VO
 */
@Data
public class FormSubmitExterRequestVO {

    @Schema(description = "部门ID", example = "f4319fa3-e9a2-4ff6-92e1-21cce6f5420d", required = true)
    private String businessGroupId;

    @Schema(description = "项目ID", example = "06b38195-3728-4523-a9b5-1331aafaa1f2", required = true)
    private String groupId;

    @Schema(description = "用户id", example = "d29f5ddb-8c40-4004-8842-65522b69e9a1")
    private String userId;
}
