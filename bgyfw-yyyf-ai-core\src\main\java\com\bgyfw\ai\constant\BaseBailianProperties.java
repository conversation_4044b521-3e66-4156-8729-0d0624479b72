package com.bgyfw.ai.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 百炼 API 配置属性
 *
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
@ConfigurationProperties(prefix = "bailian.api")
@Component
public class BaseBailianProperties {

    /**
     * 百炼 API 基础 URL，默认为阿里云百炼平台地址
     */
    private String baseUrl = "https://dashscope.aliyuncs.com/api/v1/apps";

    /**
     * 默认的应用 ID
     */
    private String defaultAppId;

    /**
     * 默认的 API Token
     */
    private String defaultToken;

    /**
     * 请求超时时间（毫秒），默认 30 秒
     */
    private Integer timeout = 30000;

    /**
     * 连接超时时间（毫秒），默认 10 秒
     */
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒），默认 30 秒
     */
    private Integer readTimeout = 30000;

    /**
     * 是否启用重试机制
     */
    private Boolean retryEnabled = false;

    /**
     * 重试次数，默认 3 次
     */
    private Integer retryCount = 3;
}
