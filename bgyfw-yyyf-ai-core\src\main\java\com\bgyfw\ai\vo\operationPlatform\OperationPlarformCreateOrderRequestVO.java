package com.bgyfw.ai.vo.operationPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.io.Serializable;

/**
 * 运维平台创建订单提交类
 *
 * <AUTHOR>
 * @date 2025/5/26 9:50
 */
@Data
public class OperationPlarformCreateOrderRequestVO implements Serializable {

    //    @Schema(description = "链路Id", example = "1")
//    @ToolParam(description = "链路Id")
//    private String traceId;
    @ToolParam(description = "导数类型")
    private String exportType;
    @ToolParam(description = "标题")
    private String title;
    @ToolParam(description = "取数开始时间段")
    private String startTime;
    @ToolParam(description = "取数结束时间段")
    private String endTime;
    @ToolParam(description = "组织名称")
    private String orgName;
    @ToolParam(description = "组织编码")
    private String orgCode;
    @ToolParam(description = "执行时间")
    private String execTime;
    @ToolParam(description = "流程申请人")
    private String applyUser;
    @ToolParam(description = "数据用途说明")
    private String dataUse;
    @ToolParam(description = "预估导出数据量")
    private String exportCount;
    @ToolParam(description = "流程单号")
    private String orderNo;
    @ToolParam(description = "导数结果接收人(BIP)")
    private String sendEmailBip;
    //股权/板块/区域/组织
    @ToolParam(description = "单位层级")
    private String orgType;

    @ToolParam(description = "数据使用周期")
    private String dataUseCycle;
    @Schema(description = "工作流类型：0：日期导数类")
    @ToolParam(description = "工作流类型")
    private Integer workFlowType;

    @ToolParam(description = "导数频次 如：单次，期间内一天一次")
    private String frequency;

    @ToolParam(description = "导数执行开始时间段")
    private String exportStartTime;
    @ToolParam(description = "导数执行结束时间段")
    private String exportEndTime;
    @ToolParam(description = "每日执行时间 如：09:00:00")
    private String everyExportTime;
    @ToolParam(description = "流程编号 如：CWJC-SJDC25062000004")
    private String flowNumber;
    @Schema(description = "运维平台上传文件信息，可复用")
    private FileUploadResponseVO fileUploadResponseVO;
    @Schema(description = "是否定时执行计划触发")
    private Boolean isSchedule = false;
}
