package com.bgyfw.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/6/13 16:02
 */
@EnableAsync
@Configuration
@Slf4j
public class ThreadExecutorConfig {

    @Value("${thread.pool.core.pool.size:10}")
    private int threadPoolCorePoolSize;
    @Value("${thread.pool.max.pool.size:50}")
    private int threadPoolMaxPoolSize;
    @Value("${thread.pool.queue.capacity:50}")
    private int threadPoolQueueCapacity;
    @Value("${thread.pool.keep.alive.seconds:300}")
    private int threadPoolKeepAliveSeconds;
    @Value("${thread.pool.await.termination.seconds:10800}")
    private int threadAwaitTerminationSeconds;

    @Bean("aiHubTaskExecutor")
    public ThreadPoolTaskExecutor aiHubTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 配置核心线程数
        executor.setCorePoolSize(threadPoolCorePoolSize);
        // 设置最大线程数
        executor.setMaxPoolSize(threadPoolMaxPoolSize);
        // 设置队列容量
        executor.setQueueCapacity(threadPoolQueueCapacity);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(threadPoolKeepAliveSeconds);
        // 配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service-");

        // 设置拒绝策略
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待所有任务结束的最长时间
        executor.setAwaitTerminationSeconds(threadAwaitTerminationSeconds);
        // 执行初始化
        executor.initialize();

        log.info("创建一个线程池 threadPoolCorePoolSize is [" + threadPoolCorePoolSize + "] threadPoolMaxPoolSize is [" + threadPoolMaxPoolSize + "] threadPoolQueueCapacity is [" + threadPoolQueueCapacity +
                "] threadPoolKeepAliveSeconds is [" + threadPoolKeepAliveSeconds + "].");

        return executor;
    }
}
