package com.bgyfw.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FlowFallBackEntity {
    @JsonProperty("operateType")
    private String operateType;

    @JsonProperty("triggerNode")
    private String triggerNode;

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("requestName")
    private String requestName;

    @JsonProperty("flowStatus")
    private String flowStatus;

    @JsonProperty("operateDatetime")
    private String operateDatetime;

    @JsonProperty("workflowId")
    private String workflowId;

    @JsonProperty("workflowName")
    private String workflowName;

    @JsonProperty("workflowDesc")
    private String workflowDesc;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("serialNumber")
    private String serialNumber;

    @JsonProperty("nodeOperator")
    private String nodeOperator;

    @JsonProperty("formdata")
    private String formdata;

    @JsonProperty("formfield")
    private String formfield;
}
