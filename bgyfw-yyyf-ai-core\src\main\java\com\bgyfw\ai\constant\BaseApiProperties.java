package com.bgyfw.ai.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class BaseApiProperties {
    /**
     * api 调用方appId
     */
    private String appId;

    /**
     * 租户密钥
     */
    private String tenantKey;

    /**
     * api 调用方appSecret
     */
    private String appSecret;

    /**
     * 域名
     */
    private String host;


    /**
     * 发起流程路径
     */
    private String processDetailPath;

    /**
     * 获取oa用户id路径
     */
    private String OaUserIdPath;
}
