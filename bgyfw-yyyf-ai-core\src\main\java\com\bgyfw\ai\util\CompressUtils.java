package com.bgyfw.ai.util;

import cn.hutool.core.collection.CollUtil;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * 压缩工具类
 *
 * <AUTHOR>
 * @date 2025/6/16 16:11
 */
public class CompressUtils {

    /**
     * 将多个文本文件压缩为ZIP并返回ZIP文件路径
     *
     * @param filePathList  要压缩的文件路径列表
     * @param outputZipPath 输出的ZIP文件路径
     * @return 生成的ZIP文件路径
     * @throws IOException              如果压缩过程中出现IO错误
     * @throws IllegalArgumentException 如果输入参数无效
     */
    public static String compressTextFilesToZip(List<String> filePathList, Path outputZipPath)
            throws IOException, IllegalArgumentException {

        if (CollUtil.isEmpty(filePathList)) {
            throw new IllegalArgumentException("文件路径列表不能为空");
        }
        List<Path> filePaths = new ArrayList<>();
        filePathList.forEach(filePath -> {
            filePaths.add(Paths.get(filePath));
        });

        // 参数校验
        Objects.requireNonNull(filePaths, "文件路径列表不能为null");
        Objects.requireNonNull(outputZipPath, "输出ZIP路径不能为null");

        if (filePaths.isEmpty()) {
            throw new IllegalArgumentException("文件路径列表不能为空");
        }

        // 确保输出目录存在
        Path parentDir = outputZipPath.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }

        try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(outputZipPath))) {
            for (Path filePath : filePaths) {
                // 验证每个文件是否存在且是文本文件
                if (!Files.exists(filePath)) {
                    throw new FileNotFoundException("文件不存在: " + filePath);
                }
                if (!Files.isRegularFile(filePath)) {
                    throw new IOException("路径不是常规文件: " + filePath);
                }

                // 创建ZIP条目(使用文件名作为条目名)
                ZipEntry zipEntry = new ZipEntry(filePath.getFileName().toString());
                zos.putNextEntry(zipEntry);

                // 写入文件内容
                Files.copy(filePath, zos);

                zos.closeEntry();
            }
        }

        // 返回生成的ZIP文件路径
        return outputZipPath.toAbsolutePath().toString();
    }

    /**
     * 将多个文件压缩到一个ZIP文件中
     *
     * @param filePaths     要压缩的文件路径列表
     * @param outputZipPath 输出的ZIP文件路径
     * @throws IOException 如果压缩过程中出现IO错误
     */
    public static void compressToZip(List<String> filePaths, String outputZipPath) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputZipPath))) {
            for (String filePath : filePaths) {
                addToZipFile(filePath, zos);
            }
        }
    }

    /**
     * 将单个文件添加到ZIP输出流
     *
     * @param filePath 文件路径
     * @param zos      ZIP输出流
     * @throws IOException 如果添加过程中出现IO错误
     */
    private static void addToZipFile(String filePath, ZipOutputStream zos) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filePath);
        }

        // 使用文件名作为ZIP条目名称
        ZipEntry zipEntry = new ZipEntry(file.getName());
        zos.putNextEntry(zipEntry);

        // 将文件内容写入ZIP
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
        }
        zos.closeEntry();
    }


    public static void main(String[] args) {
        // 示例文件列表
        List<String> filesToCompress = List.of(
                "D:/documents/file1.txt",
                "D:/documents/file2.txt",
                "D:/documents/file3.txt"
        );

        // 输出ZIP文件路径
        String outputZip = "D:/documents/output.zip";

        try {
            // 执行压缩
            compressToZip(filesToCompress, outputZip);
            System.out.println("ZIP压缩包创建成功: " + outputZip);
        } catch (IOException e) {
            System.err.println("创建ZIP压缩包时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
