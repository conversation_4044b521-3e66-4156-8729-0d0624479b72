package com.bgyfw.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 开放API签名配置
 *
 * <AUTHOR>
 * @date 2025/4/5 14:43
 */
@Data
@Component
@ConfigurationProperties(prefix = "api.open.sign")
public class OpenApiSignProperties {
    
    /**
     * 是否开启签名校验
     */
    private Boolean enable = true;
    
    /**
     * appKey和appSecret的映射配置
     */
    private Map<String, String> apps;
}