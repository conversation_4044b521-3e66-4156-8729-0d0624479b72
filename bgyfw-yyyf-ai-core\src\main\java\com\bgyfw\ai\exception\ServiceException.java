package com.bgyfw.ai.exception;

/**
 * 业务service层异常
 *
 * <AUTHOR>
 */
public class ServiceException extends CommonException {


    public ServiceException(String module, Integer code, Object[] args, String message) {
        super(module, code, args, message);
    }

    public ServiceException(String module, Integer code, Object[] args) {
        super(module, code, args);
    }

    public ServiceException(String module, Integer code, String message) {
        super(module, code, message);
    }

    public ServiceException(String module, String message) {
        super(module, message);
    }

    public ServiceException(Integer code, Object[] args) {
        super(code, args);
    }

    public ServiceException(Integer code, String message) {
        super(code, message);
    }

    public ServiceException(String message) {
        super(message);
    }
}
