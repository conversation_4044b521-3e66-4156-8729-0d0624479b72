package com.bgyfw.ai.enums;


/**
 * 操作类型
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public enum HandleTypeEnum {
    CALL_BACK(1, "回调"),
    SEND_DIFY(2, "发送dify平台"),
    MCP(3, "mcp处理"),
    CALL_BACK_SYNC(4, "异步处理"),
    ;
    private Integer code;
    private String desc;

    private HandleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
