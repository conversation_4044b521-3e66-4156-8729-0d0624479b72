package com.bgyfw.ai.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:54
 */
@Component
@Slf4j
public class CommonUtils {
    /**
     * 提取dataKey对应的值
     * 输出JSON结果
     *
     * @param array
     * @return
     */
    public Map<String, Object> dataKeyJsonResult(JSONArray array) {
        Map<String, Object> result = new LinkedHashMap<>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject item = array.getJSONObject(i);
            JSONObject formField = item.getJSONObject("formField");

            if (formField != null) {
                String dataKey = formField.getString("dataKey");
                Object content = item.get("content");

                // 如果没有content，尝试从dataOptions[0].content获取
                if (content == null && item.containsKey("dataOptions")) {
                    JSONArray dataOptions = item.getJSONArray("dataOptions");
                    StringBuilder sb = new StringBuilder();
                    for (int j = 0; j < dataOptions.size(); j++) {
                        JSONObject opt = dataOptions.getJSONObject(j);
                        if (opt.containsKey("content")) {
                            if (sb.length() > 0) {
                                sb.append(" ");
                            }
                            sb.append(opt.getString("content"));
                        }
                    }
                    content = sb.toString();
                }

                if (dataKey != null && content != null) {
                    result.put(dataKey, content);
                }
            }
        }
        return result;
    }

    /**
     * post请求
     *
     * @param params
     * @param path
     * @return
     */
    public String postRequest(String params, String path) {
        String response = HttpRequest.post(path)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(params))
                .execute()
                .body();
        return response;
    }

}
