package com.bgyfw.ai.vo.operationPlatform;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.net.HttpCookie;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 登录响应结果VO
 *
 * <AUTHOR>
 * @date 2025/5/22 10:24
 */

/**
 * 登录响应结果VO
 */
@Data
public class LoginResponseVO {
    @Schema(description = "登录是否成功", example = "true")
    private Boolean loginSuccess;

    @Schema(description = "返回消息", example = "登录成功")
    private String message;

    @Schema(description = "Cookie列表")
    private List<HttpCookie> cookies;

    public Map<String, String> getCookiesAsMap() {
        return cookies.stream()
                .collect(Collectors.toMap(
                        HttpCookie::getName,
                        HttpCookie::getValue
                ));
    }
}
