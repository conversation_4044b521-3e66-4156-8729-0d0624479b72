package com.bgyfw.ai.constant;

import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * OA常量
 */
public class OAConstant {


    /**
     * 环境域名
     */
    public static final String BASE_DOMAIN = "https://api.bgyfw.com/";

    /**
     * api sdk 版本
     */
    public static final String API_SDK_VERSION = "1.0.1";
    /**
     * 加签标识
     */
    public static final String SIGN_HEADER = "sign";
    /**
     * 接入方appId
     */
    public static final String APPID = "appId";

    /**
     * 租户密钥
     */
    public static final String TENANT_KEY = "tenantKey";

    /**
     * 时间戳
     */
    public static final String TIMESTAMP = "timestamp";

    /**
     * api请求上游时带的requestId;
     */
    public static final String REQUEST_ID = "requestId";

    /**
     * 平台标识
     */
    public static final String ACCESS_ID = "accessId";

    /**
     * 业务参数
     */
    public static final String BIZ_CONTENT = "bizContent";


    /**
     * 请求失败编码
     */
    public static final String API_REQUEST_ERROR_CODE = "-1";

    @Getter
    public enum ProfileType {
        /**
         * 对应数据库内的，status字段
         */

        DEV("dev", "开发环境"),
        TEST("test", "测试环境"),
        PROD("prod", "生产环境");
        private final String value;
        private final String desc;

        ProfileType(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static ProfileType getByValue(String value) {
            for (ProfileType e : ProfileType.values()) {
                if (Objects.equals(e.getValue(), value)) {
                    return e;
                }
            }
            return null;
        }

    }

    /**
     * 获取api请求的全路径
     *
     * @param profile 环境变量
     * @param path    接口地址，
     * @return String
     */
    public static String getApiUrl(String profile, String path) {
        ProfileType profileType = ProfileType.getByValue(profile);
        if (ObjectUtils.isEmpty(profileType)) {
            throw new RuntimeException("未知的环境变量名称");
        }
        return BASE_DOMAIN + profileType.getValue() + path;
    }
}
