package com.bgyfw.ai.vo.bailian;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 百炼 API 聊天响应 VO
 *
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
public class BailianChatResponseVO implements Serializable {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 使用情况统计
     */
    private Usage usage;

    /**
     * 请求 ID
     */
    private String request_id;

    @Data
    public static class Output {
        /**
         * 完成原因
         */
        private String finish_reason;

        /**
         * 会话 ID
         */
        private String session_id;

        /**
         * 生成的文本内容
         */
        private String text;
    }

    @Data
    public static class Usage {
        /**
         * 模型使用情况列表
         */
        private List<ModelUsage> models;
    }

    @Data
    public static class ModelUsage {
        /**
         * 输出 token 数量
         */
        private Integer output_tokens;

        /**
         * 模型 ID
         */
        private String model_id;

        /**
         * 输入 token 数量
         */
        private Integer input_tokens;
    }
}
