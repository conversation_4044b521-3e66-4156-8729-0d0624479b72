package com.bgyfw.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Agent API 签名配置
 *
 * <AUTHOR>
 * @date 2025/8/17 
 */
@Data
@Component
@ConfigurationProperties(prefix = "api.agent.sign")
public class AgentApiSignProperties {
    
    /**
     * 是否开启签名校验
     */
    private Boolean enable = true;
    
    /**
     * Agent 平台配置列表
     */
    private List<AgentPlatformConfig> platforms;
    
    /**
     * Agent 平台配置
     */
    @Data
    public static class AgentPlatformConfig {
        /**
         * 平台名称
         */
        private String name;
        
        /**
         * API Key
         */
        private String apiKey;
    }
}
