package com.bgyfw.ai.util;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.bgyfw.ai.constant.OAConstant;
import lombok.NonNull;

import java.nio.charset.Charset;
import java.util.*;

/**
 * OA验签签名工具类
 *
 * <AUTHOR>
 * @date 2024/12/13 9:07
 */
public class OASignatureUtils {

    /**
     * 得到业务方签名
     *
     * @param params 参数集合不含secretkey
     * @param secret 验证接口的secretkey
     * @return String
     */
    public static String getSignByAes(@NonNull Map<String, String> params, @NonNull String secret) {
        return SecureUtil.aes(secret.getBytes()).encryptBase64(parseSignStr(params)).substring(0, 16);
    }

    /**
     * 获取业务方签名
     *
     * @param accessId  访问id
     * @param timestamp 时间戳
     * @param requestId 请求id
     * @param secret    密钥
     * @return String
     */
    public static String getSignByAes(@NonNull String accessId, String timestamp, String requestId, @NonNull String secret) {
        Map<String, String> signParams = new HashMap<>(3);
        signParams.put(OAConstant.ACCESS_ID, accessId);
        signParams.put(OAConstant.TIMESTAMP, timestamp);
        signParams.put(OAConstant.REQUEST_ID, requestId);
        return getSignByAes(signParams, secret);
    }

    /**
     * @param signature 签名串
     * @param params    参数
     * @param secret    加密密钥
     * @return boolean
     */
    public static Boolean checkSignByAes(@NonNull String signature, @NonNull Map<String, String> params, @NonNull String secret) {
        return signature.equals(getSignByAes(params, secret));
    }

    /**
     * @param signature 签名串
     * @param accessId  访问id
     * @param timestamp 时间戳
     * @param requestId 请求id
     * @param secret    加密密钥
     * @return boolean
     */
    public static Boolean checkSignByAes(@NonNull String signature, @NonNull String accessId, String timestamp, String requestId, @NonNull String secret) {
        return signature.equals(getSignByAes(accessId, timestamp, requestId, secret));
    }

    /**
     * 得到签名
     *
     * @param params 参数集合不含secretkey
     * @param secret 验证接口的secretkey
     * @return String
     */
    public static String getSignByHmacSha256(@NonNull Map<String, Object> params, @NonNull String secret) {
        return SecureUtil.hmacSha256(secret.getBytes()).digestBase64(parseSignStr(params), true).substring(0, 16);
    }

    /**
     * 获取签名
     *
     * @param appId     appId
     * @param appSecret 密钥
     * @param jsonStr   提交的json字符串
     * @return 签名值
     */
    public static String getSignByHmacSha256(@NonNull String appId, @NonNull String appSecret, @NonNull String jsonStr) {
        return getSignByHmacSha256(appId, appSecret, null, jsonStr);
    }

    /**
     * 获取签名
     *
     * @param appId     appId
     * @param appSecret 密钥
     * @param jsonStr   提交的json字符串
     * @return 签名值
     */
    public static String getSignByHmacSha256(@NonNull String appId, @NonNull String appSecret, String timestamp, @NonNull String jsonStr) {
        if (ObjectUtil.isEmpty(timestamp)) {
            timestamp = Convert.toStr(DateUtil.date().getTime());
        }
        Map<String, Object> signParams = new HashMap<>(5);
        signParams.put(OAConstant.APPID, appId);
        signParams.put(OAConstant.TIMESTAMP, timestamp);
        signParams.put(OAConstant.BIZ_CONTENT, jsonStr);
        String signByHmacSha256 = getSignByHmacSha256(signParams, appSecret);
        return signByHmacSha256;
    }

    /**
     * 获取签名
     *
     * @param appId     appId
     * @param appSecret 密钥
     * @param jsonStr   提交的json字符串
     * @return 签名值
     */
    public static String getSignByHmacSha256(@NonNull String appId, @NonNull String appSecret, @NonNull JSON jsonStr) {
        return getSignByHmacSha256(appId, appSecret, jsonStr.toString());
    }

    /**
     * @param signature 签名串
     * @param params    参数
     * @param secret    加密密钥
     * @return boolean
     */
    public static Boolean checkSignByHmacSha256(@NonNull String signature, @NonNull Map<String, Object> params, @NonNull String secret) {
        return signature.equals(getSignByHmacSha256(params, secret));
    }

    /**
     * 校验签名
     *
     * @param signature  签名
     * @param appId      appId
     * @param timestamp  timestamp
     * @param bizContent bizContent
     * @param secret     密钥
     * @return Boolean
     */
    public static Boolean checkSignByHmacSha256(@NonNull String signature, String appId, String timestamp, String bizContent, @NonNull String secret) {
        return signature.equals(getSignByHmacSha256(appId, secret, timestamp, bizContent));
    }

    /**
     * 获取get请求的bizContent
     *
     * @param url 请求url
     * @return String
     */
    public static String parseGetContent(@NonNull String url) {
        Map<CharSequence, CharSequence> queryMap = UrlQuery.of(url, Charset.defaultCharset()).getQueryMap();
        Map<String, String> paramsMap = MapUtil.isEmpty(queryMap) ? MapUtil.empty() : Convert.toMap(String.class, String.class, queryMap);
        return paramsMap.toString();
    }


    /**
     * 将参数解析成字符串参数
     *
     * @param params 参数数据
     * @return String
     */
    private static String parseSignStr(Map<String, ?> params) {
        Collection<String> keySet = params.keySet();
        List<String> list = new ArrayList<>(keySet);
        //对key键值按字典升序排序
        Collections.sort(list);
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : list) {
            stringBuilder.append(s).append("=").append(params.get(s));
        }
       // System.out.println(stringBuilder.toString());
        return stringBuilder.toString().replaceAll("\\s*", "");
    }
}
