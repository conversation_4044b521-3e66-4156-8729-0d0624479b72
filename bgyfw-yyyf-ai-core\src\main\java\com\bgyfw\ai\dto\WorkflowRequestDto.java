package com.bgyfw.ai.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/27 16:03
 */
@Data
public class WorkflowRequestDto implements Serializable {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 流程ID
     */
    private String requestId;
    /**
     * 是否获取流程基础数据，默认值：true
     */
    private Boolean isGetRequestBaseInfo;
    /**
     * 是否获取流程签字意见，默认值：true
     */
    private Boolean isGetRequestLog;
    /**
     * 是否获取流程表单数据，默认值：true
     */
    private Boolean isGetFormData;
}
