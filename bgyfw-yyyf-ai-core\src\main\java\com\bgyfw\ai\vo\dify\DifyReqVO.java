package com.bgyfw.ai.vo.dify;

import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:53
 */
@Data
public class DifyReqVO implements Serializable {

    /**
     * 用户输入/提问内容。
     */
    private String query;

    /**
     * 传入 App 定义的各变量值，键值对形式。
     * 如果变量是文件类型，请指定 files 字段。
     * 默认值为 {}。
     */
    private Map<String, Object> inputs;

    /**
     * 返回模式：
     * streaming：流式模式（推荐）；
     * blocking：阻塞模式（可能超时）。
     */
    private String response_mode;

    /**
     * 用户标识，需在应用内唯一，用于身份识别和统计。
     */
    private String user;

    /**
     * 会话 ID，用于继续之前的对话（可选字段）。
     */
    private String conversation_id;

    /**
     * 文件列表，支持文档、图片、音频、视频、自定义等类型。
     * 仅当模型支持 Vision 能力时可用。
     */
    private List<FileItem> files;

    @Data
    public static class FileItem {

        /**
         * 文件类型：
         * document（如 PDF、DOCX）、image（如 JPG、PNG）、
         * audio（如 MP3）、video（如 MP4）、
         * custom（其他类型）。
         */
        private String type;

        /**
         * 文件传递方式：
         * remote_url（远程链接）或 local_file（上传文件）。
         */
        private String transfer_method;

        /**
         * 图片地址，仅当 transfer_method = remote_url 时填写。
         */
        private String url;

        /**
         * 上传文件的 ID，仅当 transfer_method = local_file 时填写。
         */
        private String upload_file_id;
    }

}

