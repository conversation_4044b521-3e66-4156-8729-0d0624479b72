package com.bgyfw.ai.service.client;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.bgyfw.ai.vo.operationPlatform.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.net.HttpCookie;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 运维平台工具类
 *
 * <AUTHOR>
 * @date 2025/5/23 16:17
 */
@Component
@Slf4j
public class OperationPlatformApiUtil {
    // 从Nacos获取运维平台导数配置
    @Value("${yunwei.ncc.baseUrl:'https://opex-test.bgyfw.com:8443'}")
    private String baseUrl;

    @Value("${yunwei.ncc.category:'GENERIC_SERVICE'}")
    private String category;
    /**
     * 部门id
     */
    @Value("${yunwei.ncc.businessGroupId:'f4319fa3-e9a2-4ff6-92e1-21cce6f5420d'}")
    private String businessGroupId;
    /**
     * 项目id
     */
    @Value("${yunwei.ncc.projectId:'06b38195-3728-4523-a9b5-1331aafaa1f2'}")
    private String projectId;

    /**
     * 处理流程id【固定值】
     */
    @Value("${yunwei.ncc.processDefinitionId:'25eaa111-6634-4858-acc7-6cbcf1d4439d'}")
    private String processDefinitionId;
    /**
     * 导数任务流水线ID（固定值）
     */
    @Value("${yunwei.ncc.pipelineConfigId:'184f3909-c296-449a-b41d-b8fa97d97a79'}")
    private String pipelineConfigId;
    /**
     * 申请人账户ID
     */
    @Value("${yunwei.ncc.export.requestUserId:'d29f5ddb-8c40-4004-8842-65522b69e9a1'}")
    private String requestUserId;

    /**
     * 导数流程审批通过后发送接收邮件的bip，空则为发送给提交人
     */
    @Value("${yunwei.ncc.export.sendEmailBip:'zhousongjie'}")
    private String sendEmailBip;
    /**
     * 流水线id【固定值】
     */
    @Value("${yunwei.ncc.pipelineId:'6eabd0dd-a956-4aad-95c9-3c4660fe55e6'}")
    private String pipelineId;
    /**
     * 提交工单ID（固定值）
     */
    @Value("${yunwei.ncc.createOrderId:'1adbfc00-eeb8-4a3c-8e71-a6485c23bd75'}")
    private String createOrderId;
    /**
     * 登录运维平台 账号
     */
    @Value("${yunwei.ncc.export.userName:'ncc_xietong_user'}")
    private String userName;
    /**
     * 登录运维平台 密码
     */
    @Value("${yunwei.ncc.export.password:'Xv9@pRm2!Te6qA#ZNCC'}")
    private String password;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String COOKIE_KEY = "ncc:api:cookies";
    private static final int COOKIE_TTL = 7000; // 2小时

    // 通用请求头配置
    private Map<String, String> getCommonHeaders() {
        return Map.of(
                "Accept", "application/json, text/plain, */*",
                "Accept-Language", "zh",
                "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "sec-ch-ua", "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                "sec-ch-ua-mobile", "?0",
                "sec-ch-ua-platform", "\"Windows\""
        );
    }

    /**
     * 登录并缓存Cookie
     */
    public LoginResponseVO login(LoginRequestVO request) {
        String url = baseUrl + "/platform-api/login";
        HttpResponse response = HttpRequest.post(url)
                .headerMap(getCommonHeaders(), true)
                .form(JSONUtil.parseObj(request).toBean(Map.class))
                .execute();

        if (response.getStatus() != 200) {
            throw new RuntimeException("登录失败，状态码：" + response.getStatus());
        }
        LoginResponseVO res = JSONUtil.toBean(response.body(), LoginResponseVO.class);
        if (!res.getLoginSuccess()) {
            throw new RuntimeException("登录失败：" + res.getMessage());
        }
// 获取Hutool的Cookie列表并转换
        List<HttpCookie> cookieList = response.getCookies();
        res.setCookies(cookieList);
        // 转换为Map存储
        Map<String, String> cookieMap = cookieList.stream()
                .collect(Collectors.toMap(
                        HttpCookie::getName,
                        HttpCookie::getValue
                ));
        redisTemplate.opsForHash().putAll(COOKIE_KEY, cookieMap);
        redisTemplate.expire(COOKIE_KEY, COOKIE_TTL, TimeUnit.SECONDS);
        return res;
    }

    private void clearCookie() {
        //清除cookies
        redisTemplate.delete(COOKIE_KEY);
    }

    /**
     * 上传文件（自动处理Cookie）
     */
    public FileUploadResponseVO uploadFile(String filePath, Boolean isRetry, Integer retryCount) {
        checkAndRefreshCookie();
        String url = baseUrl + "/platform-api/icon/upload";
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在：" + filePath);
        }
        HttpResponse response = HttpRequest.post(url)
                .headerMap(getCommonHeaders(), true)
                .cookie(getCachedCookies()) // 从缓存获取Cookie
                .form("file", file)
                .execute();
        //过期
        if (response.getStatus() == 401 && isRetry && retryCount > 0) {
            retryCount--;
            clearCookie();
            return uploadFile(filePath, isRetry, retryCount);
        }
        log.info("文件上传返回：{}", response.body());
        if (response.getStatus() != 200) {
            log.error("文件上传失败:{}", response);
            throw new RuntimeException("文件上传失败：重试剩余次数：" + retryCount + "状态：" + response.getStatus());
        }
        return JSONUtil.toBean(response.body(), FileUploadResponseVO.class);
    }

    /**
     * 创建工单（自动处理Cookie）
     */
    private String createOrder(FormSubmitRequestVO request, Boolean isRetry, Integer retryCount) {
        checkAndRefreshCookie();
        String url = baseUrl + "/platform-api/catalogs/provision/deployment/" + createOrderId;
        HttpResponse response = HttpRequest.post(url)
                .headerMap(getCommonHeaders(), true)
                .header("Content-Type", "application/json")
                .cookie(getCachedCookies())
                .body(JSONUtil.toJsonStr(request))
                .execute();
        //过期
        if (response.getStatus() == 401 && isRetry && retryCount > 0) {
            retryCount--;
            clearCookie();
            return createOrder(request, isRetry, retryCount);
        }
        if (response.getStatus() != 200) {
            throw new RuntimeException("工单创建失败，状态码：" + response.getStatus());
        }
        log.info("创建工单返回：{}", response.body());
        //将结果反序列化成对象CreateOrderResponseVO
        return response.body();
    }

    /**
     * 创建导出工单
     *
     * @param fileUploadList
     * @param requestVO
     * @return
     */
    public String createExportOrder(List<FileUploadResponseVO> fileUploadList, OperationPlarformCreateOrderRequestVO requestVO, Boolean isRetry) {
        FormSubmitRequestVO formSubmitRequestVO = getFormSubmitRequestVO(fileUploadList, requestVO.getTitle(), requestVO.getExecTime(), requestVO.getDataUse(), requestVO.getExportCount(), requestVO.getSendEmailBip(), requestVO.getDataUseCycle(), requestVO.getFlowNumber());
        return createOrder(formSubmitRequestVO, isRetry, 3);
    }


    /**
     * 组装请求体
     *
     * @param fileUploadList
     * @param title
     * @return
     */
    private FormSubmitRequestVO getFormSubmitRequestVO(List<FileUploadResponseVO> fileUploadList, String title, String execTime, String dataUse, String exportCount, String sendBips, String dataUseCycle, String flowNumber) {
        FormSubmitRequestVO formSubmitRequestVO = new FormSubmitRequestVO();
        ProcessForm processForm = new ProcessForm();
        title = title + "-" + flowNumber;
        processForm.setURGENCY("MEDIUM");
        processForm.setIMPACT("MODERATE");
        processForm.setPRIORITY("NORMAL");
        processForm.setQ2(StrUtil.isNotBlank(dataUseCycle) ? dataUseCycle : "1周后销毁");
        processForm.setW1(StrUtil.isNotBlank(exportCount) ? exportCount : "1万以内");
        processForm.setWW(title);
        if (StrUtil.isNotBlank(execTime)) {
            processForm.setExec_date(DateUtil.parse(execTime).getTime());
        }
        String sendBipStr = "";
        if (StrUtil.isNotBlank(sendBips)) {
            //sendEmailBip 用逗号拼接
            sendBipStr = StrUtil.isNotBlank(sendEmailBip) ? sendEmailBip + "," + getSendEmailBip(sendBips) : getSendEmailBip(sendBips);
        } else {
            sendBipStr = sendEmailBip;
        }
        processForm.setDingding_success_receiver(sendBipStr);
        processForm.setSystem_name("NCC");
        List<DbItem> dbList = new ArrayList<>();
        fileUploadList.forEach(fileUploadResponseVO -> {
            DbItem dbItem = new DbItem();
            dbItem.setName(fileUploadResponseVO.getFileName());
            dbItem.setSize(fileUploadResponseVO.getSize());
            dbItem.setStatus("done");
            dbItem.setId(fileUploadResponseVO.getId());
            dbItem.setUid(fileUploadResponseVO.getUid());
            dbList.add(dbItem);
        });
        processForm.setDb_0(dbList);
        processForm.setSchemaFormValid(true);
        FormSubmitExterRequestVO formSubmitExterRequestVO = new FormSubmitExterRequestVO();
        formSubmitExterRequestVO.setBusinessGroupId(businessGroupId);
        formSubmitExterRequestVO.setGroupId(projectId);
        formSubmitExterRequestVO.setUserId(getUserId());
        processForm.setExternalParams(JSONUtil.toJsonStr(formSubmitExterRequestVO));

        formSubmitRequestVO.setProcessForm(processForm);
        formSubmitRequestVO.setName(title);
        formSubmitRequestVO.setDescription(StrUtil.isNotBlank(dataUse) ? dataUse : title);

        //全局参数列表
        List<GlobalParameter> globalParameters = new ArrayList<>();
        GlobalParameter globalParameter = new GlobalParameter();
        globalParameter.setDefaultValue("${context.globalParameters.systemContext.genericRequest.id}");
        globalParameter.setId("workorder_id");
        globalParameters.add(globalParameter);

        ExecuteParameters executeParameters = new ExecuteParameters();
        executeParameters.setGlobalParameters(globalParameters);

        IntegrationTaskConfig integrationTaskConfig = new IntegrationTaskConfig();
        integrationTaskConfig.setPipelineConfigId(pipelineConfigId);
        integrationTaskConfig.setExecuteParameters(executeParameters);
        integrationTaskConfig.setTaskDefinitionName("导数流水线");

        Map<String, IntegrationTaskConfig> integrationTaskMap = new HashMap<>();
        integrationTaskMap.put(pipelineId, integrationTaskConfig);

        RequestParameters requestParameters = new RequestParameters();
        requestParameters.setIntegrationTaskConfig(integrationTaskMap);
        formSubmitRequestVO.setRequestParameters(requestParameters);


        formSubmitRequestVO.setBusinessGroupId(businessGroupId);
        formSubmitRequestVO.setProjectId(projectId);
        formSubmitRequestVO.setProcessDefinitionId(processDefinitionId);
        formSubmitRequestVO.setPipelineConfigId(pipelineConfigId);
        formSubmitRequestVO.setRequestUserId(getUserId());
        formSubmitRequestVO.setOwnerId(getUserId());
        formSubmitRequestVO.setApplicant(getUserName());
        formSubmitRequestVO.setCategory("GENERIC_SERVICE");
        return formSubmitRequestVO;
    }

    /**
     * 获取发送邮箱处理中英文逗号拼接兼容问题
     *
     * @param sendEmailBipStr
     * @return
     */
    private String getSendEmailBip(String sendEmailBipStr) {
        String result = "";
        if (StrUtil.isNotBlank(sendEmailBipStr)) {
            if (sendEmailBipStr.contains(",") || sendEmailBipStr.contains("，")) {
                sendEmailBipStr = sendEmailBipStr.replace("，", ",");
                return sendEmailBipStr;
            } else {
                result = sendEmailBipStr;
            }
        }
        return result;
    }

    /**
     * 检查Cookie有效性并刷新
     */
    private void checkAndRefreshCookie() {
        if (!redisTemplate.hasKey(COOKIE_KEY)) {
            LoginRequestVO request = new LoginRequestVO();
//            request.setUsername("ncc_xietong_user");
//            request.setPassword("Xv9@pRm2!Te6qA#ZNCC");
            request.setUsername(userName);
            request.setPassword(password);
            request.setEncrypted("false");
            request.setLoginType("Normal");
            login(request);
        }
    }

    /**
     * 获取缓存的Cookie
     */
    private List<HttpCookie> getCachedCookies() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(COOKIE_KEY);
        return entries.entrySet().stream()
                .map(entry -> {
                    String cookieName = entry.getKey().toString();
                    String cookieValue = entry.getValue().toString();
                    HttpCookie cookie = new HttpCookie(cookieName, cookieValue);
                    // 可选：设置其他属性如domain、path、maxAge等（根据存储时的逻辑补充）
                    return cookie;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取用户名
     *
     * @return
     */
    private String getUserName() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(COOKIE_KEY);
        //获取key为username的值
        String username = entries.entrySet().stream()
                .filter(entry -> entry.getKey().toString().equals("username"))
                .findAny()
                .map(Map.Entry::getValue)
                .map(Object::toString)
                .orElse(null);
        //如果username不为空，则反编码utf-8
        return username != null ? URLDecoder.decode(username, StandardCharsets.UTF_8) : null;
    }

    /**
     * 获取用户ID
     *
     * @return
     */
    private String getUserId() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(COOKIE_KEY);
        //获取key为userId的值
        return entries.entrySet().stream()
                .filter(entry -> entry.getKey().toString().equals("userId"))
                .findAny()
                .map(Map.Entry::getValue)
                .map(Object::toString)
                .orElse(null);
    }

    /**
     * 写入文件
     *
     * @param content
     * @param fileName
     * @return
     */
    public String writeToFile(String content, String fileName) {
        String filePath = getFileName(fileName, "txt");
        // 将路径字符串转换成 Path 对象
        Path file = Paths.get(filePath);
        try {
            // 如果目录不存在，则创建目录
            Path directory = file.getParent();
            if (directory != null && !Files.exists(directory)) {
                Files.createDirectories(directory);
                log.info("目录创建成功：" + directory);
            }
            // 写入内容到文件中，如果文件已存在则覆盖
            Files.write(file, content.getBytes(StandardCharsets.UTF_8));
            log.info("文件写入成功：" + file);
        } catch (IOException e) {
            log.error("文件操作失败：" + e.getMessage());
        }
        return file.toAbsolutePath().toString();
    }

    /**
     * 获取文件名
     *
     * @param fileName
     * @param extension
     * @return
     */
    public String getFileName(String fileName, String extension) {
        return "/file/ncc/export/" + getFileName(fileName) + "." + extension;
    }

    /**
     * 生成文件名
     *
     * @return
     */
    private String getFileName(String fileName) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
//        String datePart = dateFormat.format(new Date());
        // 生成当前时间戳（确保唯一性）
        long timestamp = System.currentTimeMillis();
        return fileName + timestamp;
    }

    public static void main(String[] args) {
        OperationPlatformApiUtil operationPlatformApiUtil = new OperationPlatformApiUtil();
        String path = operationPlatformApiUtil.writeToFile("hello world", "test");
        System.out.println(path);
        FileUploadResponseVO fileUploadResponseVO = operationPlatformApiUtil.uploadFile(path, true, 3);
        System.out.println(fileUploadResponseVO);
    }

    // 在Spring Boot服务中注入工具类
//    @Autowired
//    private NCCApiUtil nccApiUtil;
//
//    public void createOrderDemo() {
//        // 1.自动登录（通过check机制）
//
//        // 2.上传文件
//        FileUploadResponse fileRes = nccApiUtil.uploadFile("/data/ncc_data.sql");
//
//        // 3.创建工单
//        OrderCreateRequest orderRequest = new OrderCreateRequest();
//        orderRequest.setFileInfo(fileRes);
//        orderRequest.setSqlFileName("ncc_data.sql");
//        Object orderRes = nccApiUtil.createOrder(orderRequest);
//    }

}
