package com.bgyfw.ai.vo.operationPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 全局参数
 *
 * <AUTHOR>
 * @date 2025/5/23 14:31
 */
@Data
public  class GlobalParameter {
    @Schema(description = "参数默认值", example = "${context.globalParameters.systemContext.genericRequest.id}")
    private String defaultValue;

    @Schema(description = "参数ID", example = "workorder_id")
    private String id;
}
