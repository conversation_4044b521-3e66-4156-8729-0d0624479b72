package com.bgyfw.ai.utils;


import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 签名工具类
 * <AUTHOR>
 * @date 2025/4/11 10:30
 */
public class SignUtil {

    /**
     * 时间戳格式
     */
    private static final String TIMESTAMP_PATTERN = "yyyyMMddHHmmss";

    /**
     * 时间戳有效期(分钟)
     */
    private static final int TIMESTAMP_EXPIRE_MINUTES = 5;

    /**
     * 生成时间戳
     * @return 当前时间戳，格式yyyyMMddHHmmss
     */
    public static String generateTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat(TIMESTAMP_PATTERN);
        return sdf.format(new Date());
    }

    /**
     * 校验时间戳是否有效
     * @param timestamp 时间戳，格式yyyyMMddHHmmss
     * @return 是否有效
     */
    public static boolean checkTimestamp(String timestamp) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(TIMESTAMP_PATTERN);
            Date requestTime = sdf.parse(timestamp);
            Date currentTime = new Date();
            
            // 检查时间戳是否在有效期内
            long diff = (currentTime.getTime() - requestTime.getTime()) / (1000 * 60);
            return diff >= 0 && diff <= TIMESTAMP_EXPIRE_MINUTES;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 获取客户端真实IP
     * @param request HttpServletRequest
     * @return 客户端IP
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(ip) && !"unKnown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个IP值，第一个IP才是真实IP
            int index = ip.indexOf(",");
            if (index != -1) {
                return ip.substring(0, index);
            } else {
                return ip;
            }
        }
        ip = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(ip) && !"unKnown".equalsIgnoreCase(ip)) {
            return ip;
        }
        return request.getRemoteAddr();
    }

    /**
     * 生成签名
     * @param params 参数Map
     * @param appSecret 密钥
     * @return 签名
     */
    public static String generateSignature(Map<String, String> params, String appSecret) {
        // 将参数按照key=value&key=value格式拼接
        String paramStr = params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        
        // 在最后拼接上密钥
        paramStr = paramStr + appSecret;
        
        // MD5加密并转大写
        return DigestUtils.md5Hex(paramStr).toUpperCase();
    }

    /**
     * 从请求参数中生成签名
     * @param request 请求
     * @param appSecret 密钥
     * @return 签名
     */
    public static String generateSignatureFromRequest(HttpServletRequest request, String appSecret) {
        // 获取所有请求参数
        Map<String, String> params = new TreeMap<>();
        
        // 获取所有请求参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String name = paramNames.nextElement();
            String value = request.getParameter(name);
            
            // sign参数不参与签名
            if (!"sign".equals(name) && StringUtils.isNotBlank(value)) {
                params.put(name, value);
            }
        }
        
        // 计算签名
        return generateSignature(params, appSecret);
    }
} 