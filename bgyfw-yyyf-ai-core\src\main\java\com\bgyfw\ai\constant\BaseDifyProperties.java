package com.bgyfw.ai.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/27 16:39
 */
@Data
@ConfigurationProperties(prefix = "dify.api")
@Component
public class BaseDifyProperties {

    /**
     * dify api host 如：http://************/v1
     */
    private String host;

    /**
     * 发送对话消息 接口
     */
    private String chatUrl = "/chat-messages";

    /**
     * 上传文件接口
     */
    private String uploadUrl = "/files/upload";

}
