# bgyfw-yyyf-ai-core

运营研发 AI 基础能力核心库

## 项目简介

本项目是一个基于 Spring Boot 3 的 AI 基础能力核心库，提供统一的 AI 平台客户端封装、运维平台集成、钉钉通知等基础功能，旨在为运营研发团队提供标准化的 AI 能力支撑。

## 技术栈

### 核心框架
- **Spring Boot 3.4.5** - 主框架
- **Spring AI 1.0.0** - AI 能力集成
- **Java 17** - 开发语言

### AI 相关
- **LangChain4J Community DashScope 1.0.0-beta2** - 阿里云通义千问集成
- **Spring AI Markdown Document Reader** - 文档解析
- **JsonSchema Generator 4.38.0** - 结构化输出支持
- **Kryo 5.6.2** - 会话记忆序列化

### 数据存储
- **MyBatis-Plus 3.5.5** - ORM 框架
- **MySQL Connector** - 数据库驱动
- **Redis** - 缓存存储
- **Druid 1.2.20** - 数据库连接池

### 工具库
- **Hutool 5.8.37** - Java 工具库
- **Fastjson 1.2.58** - JSON 处理
- **Apache HttpComponents** - HTTP 客户端
- **OkHttps-Fastjson2 4.0.2** - HTTP 请求工具
- **JSoup 1.19.1** - HTML 解析

### 文档与监控
- **Knife4j 4.3.0** - API 文档
- **SpringDoc OpenAPI 2.5.0** - OpenAPI 规范
- **Spring Boot Actuator** - 应用监控

### 任务调度与消息
- **XXL-Job 2.4.0** - 分布式任务调度
- **tech-mq-springboot3-starter** - 消息总线

### 第三方集成
- **阿里云钉钉 SDK 2.0.0** - 钉钉机器人通知
- **Jose4j 0.7.8** - JWT 处理

## 功能特性

### 🤖 AI 能力
- 统一的 AI 平台客户端封装
- 支持阿里云通义千问模型
- 结构化输出支持
- 文档解析和处理
- 会话记忆持久化

### 🔧 基础工具类
- **CommonUtils** - 通用工具方法，JSON 数据处理
- **DingTalkUtils** - 钉钉机器人消息推送（支持 Markdown 和文本格式）
- **OASignatureUtils** - OA 系统签名验证工具
- **CompressUtils** - 文件压缩工具
- **OperationPlatformApiUtil** - 运维平台 API 集成工具

### 🔐 安全组件
- **AgentApiSignAspect** - Agent API 轻量级鉴权切面
- **@AgentApiSign** - Agent API 签名验证注解
- **AgentApiSignProperties** - Agent API 签名配置

### 🏗️ 核心组件
- **AiAutoConfiguration** - AI 能力自动配置
- **OpenAPIConfig** - Swagger 文档配置
- **ProcessForm/DbItem** - 运维平台数据模型

### 📋 运维集成
- 运维平台工单创建和管理
- 文件上传和处理
- 自动化导数流程
- Cookie 管理和会话保持

### 📢 通知能力
- 钉钉机器人消息推送
- 支持 @用户功能
- Markdown 格式消息支持
- 失败重试机制

### 🔒 Agent API 鉴权
- 基于 API Key 的轻量级鉴权机制
- 支持多 Agent 平台配置（Dify、Coze、阿里云百炼等）
- 灵活的平台访问控制
- 支持请求头和请求体两种传参方式

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.bgyfw.ai</groupId>
    <artifactId>bgyfw-yyyf-ai-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
bgyfw:
  ai:
    enabled: true
    default-timeout: 30000
    retry-count: 3

# 运维平台配置
yunwei:
  ncc:
    baseUrl: https://opex-test.bgyfw.com:8443
    category: GENERIC_SERVICE
    businessGroupId: f4319fa3-e9a2-4ff6-92e1-21cce6f5420d
    projectId: 06b38195-3728-4523-a9b5-1331aafaa1f2

# Redis 配置
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0

# 百炼 API 配置
bailian:
  api:
    baseUrl: https://dashscope.aliyuncs.com/api/v1/apps
    defaultAppId: 0b4df13ffcae4d7eb0a8ad866050e202
    defaultToken: sk-c4b3d14f7a714560bc2144ecef40e76b
    timeout: 30000
    connectTimeout: 10000
    readTimeout: 30000
    retryEnabled: false
    retryCount: 3

# Agent API 签名配置
api:
  agent:
    sign:
      enable: true
      platforms:
        - name: "dify"
          apiKey: "your_dify_api_key"
        - name: "coze"
          apiKey: "your_coze_api_key"
```

### 3. 使用示例

#### 钉钉消息推送
```java
@Autowired
private DingTalkUtils dingTalkUtils;

// 发送 Markdown 消息
DingTalkUtils.sendMarkdown(
    "your-token", 
    "your-secret", 
    "***********", 
    "通知标题", 
    "#### 消息内容\n> **详情**：处理完成"
);
```

#### 运维平台集成
```java
@Autowired
private OperationPlatformApiUtil operationPlatformApiUtil;

// 上传文件并创建工单
FileUploadResponse fileRes = operationPlatformApiUtil.uploadFile("/data/script.sql");
OrderCreateRequest request = new OrderCreateRequest();
request.setFileInfo(fileRes);
Object result = operationPlatformApiUtil.createOrder(request);
```

#### 百炼 API 调用
```java
@Autowired
private BailianApiClient bailianApiClient;

// 简单聊天调用
BailianChatResponseVO response = bailianApiClient.chat(
    "0b4df13ffcae4d7eb0a8ad866050e202",
    "sk-c4b3d14f7a714560bc2144ecef40e76b",
    "你是？"
);

// 带会话 ID 的聊天调用
BailianChatResponseVO response = bailianApiClient.chat(
    "0b4df13ffcae4d7eb0a8ad866050e202",
    "sk-c4b3d14f7a714560bc2144ecef40e76b",
    "继续上次的对话",
    "session-123"
);

// 使用默认配置的聊天调用
BailianChatResponseVO response = bailianApiClient.chatWithDefault("你好");
```

#### Agent API 鉴权
```java
@RestController
@RequestMapping("/api/agent")
public class AgentController {

    @PostMapping("/dify-only")
    public ResponseEntity<String> difyOnly(@RequestBody Request request) {
        return ResponseEntity.ok("success");
    }
}
```

## 项目结构

```
bgyfw-yyyf-ai-core/
├── src/main/java/com/bgyfw/ai/
│   ├── config/           # 配置类
│   │   ├── AiAutoConfiguration.java
│   │   └── OpenAPIConfig.java
│   ├── service/          # 服务层
│   │   └── client/       # 外部服务客户端
│   ├── util/             # 工具类
│   │   ├── CommonUtils.java
│   │   ├── DingTalkUtils.java
│   │   ├── OASignatureUtils.java
│   │   └── CompressUtils.java
│   └── vo/               # 数据传输对象
│   │    └── operationPlatform/
│   └── entity/           # 数据库实体
│       └── AgentInvocationLog # 智能体调用记录
└── src/main/resources/
    └── META-INF/
        └── spring.factories  # 自动配置
```

## 开发指南

### 环境要求
- JDK 17+
- Maven 3.6+
- Redis 6.0+
- MySQL 8.0+

### 构建项目
```bash
mvn clean compile
mvn clean package
```

### 发布到私服
```bash
mvn clean deploy
```

## 版本信息

- **当前版本**: 1.0.0-SNAPSHOT
- **Spring Boot**: 3.4.5
- **Java**: 17

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用内部许可证，仅供公司内部使用。
