package com.bgyfw.ai.vo.operationPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 执行参数配置
 *
 * <AUTHOR>
 * @date 2025/5/23 14:30
 */
@Data
public  class IntegrationTaskConfig {
    @Schema(description = "流水线配置ID（固定值）", example = "184f3909-c296-449a-b41d-b8fa97d97a79", required = true)
    private String pipelineConfigId;

    @Schema(description = "执行参数配置", required = true)
    private ExecuteParameters executeParameters;

    @Schema(description = "任务定义名称", example = "导数流水线")
    private String taskDefinitionName;
}
