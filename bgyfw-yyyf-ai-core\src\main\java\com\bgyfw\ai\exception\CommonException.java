package com.bgyfw.ai.exception;

import lombok.Getter;

/**
 * 基本异常，系统定义的所有异常都需要继承这个基本类
 *
 * <AUTHOR>
 */
@Getter
public class CommonException extends BaseException {

    private static final long serialVersionUID = 1L;

    public CommonException(String module, Integer code, Object[] args) {
        super(module, code, args, null);
    }

    public CommonException(String module, Integer code, String message) {
        super(module, code, message);
    }

    public CommonException(String module, String message) {
        super(module, null, null, message);
    }

    public CommonException(Integer code, Object[] args) {
        super("unknown", code, args, null);
    }

    public CommonException(Integer code, String message) {
//        super(null, code, null, message);
        super(code, message);
    }

    public CommonException(String message) {
        super("unknown", null, null, message);
    }

    public CommonException(String module, Integer code, Object[] args, String message) {
        super(module, code, args, message);
    }
}
