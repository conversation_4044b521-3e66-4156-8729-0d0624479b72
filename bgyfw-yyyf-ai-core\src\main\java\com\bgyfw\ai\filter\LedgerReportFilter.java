package com.bgyfw.ai.filter;

import com.bgyfw.ai.vo.wrapper.ContentCachingRequestWrapper;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;
import java.io.IOException;

/**
 * @ClassName LedgerReportFilter
 * @Description 用于重复读取requestBody
 * <AUTHOR>
 * @Date 2023/2/22 18:05
 * @Version 1.0
 */
@Component
public class LedgerReportFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String contentType = httpServletRequest.getContentType();
        if (contentType != null && contentType.startsWith("multipart/")) {
            // 文件上传类型
            chain.doFilter(request, response);
            return;
        }
        String requestURI = httpServletRequest.getRequestURI();
        if (requestURI.contains("/publicly") || requestURI.contains("/druid")) {
            chain.doFilter(request, response);
            return;
        }
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper((HttpServletRequest) request);
        chain.doFilter(requestWrapper, response);
    }

    @Override
    public void destroy() {

    }
}
