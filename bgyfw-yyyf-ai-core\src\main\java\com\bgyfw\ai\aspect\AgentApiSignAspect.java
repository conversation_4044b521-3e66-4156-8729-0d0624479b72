package com.bgyfw.ai.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bgyfw.ai.config.AgentApiSignProperties;
import com.bgyfw.ai.exception.ServiceException;
import com.bgyfw.ai.vo.wrapper.ContentCachingRequestWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * Agent API 接口签名验证切面
 *
 * <AUTHOR>
 * @date 2025/8/17
 */
@Slf4j
@Aspect
@Component
public class AgentApiSignAspect {

    @Resource
    private AgentApiSignProperties agentApiSignProperties;

    /**
     * 定义切入点 - 拦截指定包下的所有控制器方法（可选）
     * 可以根据实际的 Agent API 控制器包路径进行调整
     */
    @Pointcut("execution(* com.bgyfw.provider.controller.agent..*.*(..))")
    public void agentApiPackagePointCut() {
    }

    /**
     * 环绕通知 - 包路径方式
     */
    @Around("agentApiPackagePointCut()")
    public Object aroundPackage(ProceedingJoinPoint joinPoint) throws Throwable {
        // 对于包路径方式，使用默认的验证规则
        return doValidation(joinPoint);
    }

    /**
     * 执行验证逻辑
     */
    private Object doValidation(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取RequestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 从获取RequestAttributes中获取HttpServletRequest的信息
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);

        String apiKey = null;
        String platformName = null;

        // 优先从请求头获取 API Key
        apiKey = request.getHeader("X-API-Key");
        platformName = request.getHeader("X-Platform-Name");

        // 如果请求头中没有，则从请求体中获取
        if (StrUtil.isBlank(apiKey)) {
            // 判断请求内容类型
            String contentType = request.getContentType();
            if (contentType != null && contentType.contains("application/json")) {
                // 确保请求是ContentCachingRequestWrapper类型，以便能够多次读取请求体
                ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
                String body = IOUtils.toString(requestWrapper.getBody(), request.getCharacterEncoding());
                if (StrUtil.isNotBlank(body)) {
                    JSONObject jsonObject = JSONUtil.parseObj(body);
                    apiKey = jsonObject.getStr("apiKey");
                    platformName = jsonObject.getStr("platformName");
                }
            }
        }

        // 校验参数是否为空
        if (StrUtil.isBlank(apiKey)) {
            throw new ServiceException(401, "API Key 不能为空");
        }

        // 校验 API Key 是否有效
        if (Boolean.TRUE.equals(agentApiSignProperties.getEnable())) {
            boolean isValidApiKey = validateApiKey(apiKey, platformName);
            if (!isValidApiKey) {
                log.error("Agent API Key 验证失败, API Key: {}, Platform: {}", apiKey, platformName);
                throw new ServiceException(401, "API Key 验证失败");
            }
        }

        // 将 API Key 和平台名称存储到 request 中，供后续使用
        request.setAttribute("currentApiKey", apiKey);
        request.setAttribute("currentPlatformName", platformName);

        return joinPoint.proceed();
    }

    /**
     * 验证 API Key 是否有效
     *
     * @param apiKey       API Key
     * @param platformName 平台名称
     * @return 是否有效
     */
    private boolean validateApiKey(String apiKey, String platformName) {
        List<AgentApiSignProperties.AgentPlatformConfig> platforms = agentApiSignProperties.getPlatforms();
        if (platforms == null || platforms.isEmpty()) {
            log.warn("未配置任何 Agent 平台信息");
            return false;
        }

        for (AgentApiSignProperties.AgentPlatformConfig platform : platforms) {
            // 如果指定了平台名称，则需要匹配平台名称和 API Key
            if (StrUtil.isNotBlank(platformName)) {
                if (platformName.equals(platform.getName()) && apiKey.equals(platform.getApiKey())) {
                    return true;
                }
            } else {
                // 如果没有指定平台名称，则只要 API Key 匹配任一平台即可
                if (apiKey.equals(platform.getApiKey())) {
                    return true;
                }
            }
        }

        return false;
    }
}
