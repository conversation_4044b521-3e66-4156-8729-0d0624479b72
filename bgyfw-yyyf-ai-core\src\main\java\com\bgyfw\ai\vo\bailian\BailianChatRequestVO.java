package com.bgyfw.ai.vo.bailian;

import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * 百炼 API 聊天请求 VO
 *
 * <AUTHOR>
 * @date 2025/8/18
 */
@Data
public class BailianChatRequestVO implements Serializable {

    /**
     * 输入参数
     */
    private Input input;

    /**
     * 额外参数
     */
    private Map<String, Object> parameters;

    @Data
    public static class Input {
        /**
         * 用户输入的提示词（必选）
         */
        private String prompt;

        /**
         * 历史对话的唯一标识（可选）
         */
        private String session_id;

        /**
         * 是否流式输出回复（可选）
         * false（默认值）：模型生成完所有内容后一次性返回结果
         * true：边生成边输出，即每生成一部分内容就立即输出一个片段（chunk）
         */
        private Boolean stream = false;
    }
}
