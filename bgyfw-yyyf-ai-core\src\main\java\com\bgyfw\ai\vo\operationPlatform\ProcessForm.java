package com.bgyfw.ai.vo.operationPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 流程表单数据
 *
 * <AUTHOR>
 * @date 2025/5/22 10:26
 */
@Data
public  class ProcessForm {
    @Schema(description = "紧急程度", example = "MEDIUM")
    private String URGENCY;

    @Schema(description = "影响范围", example = "MODERATE")
    private String IMPACT;

    @Schema(description = "优先级", example = "NORMAL")
    private String PRIORITY;

    @Schema(description = "使用周期", example = "1周后销毁")
    private String Q2;

    @Schema(description = "导出数量", example = "1万以内")
    private String W1;

    @Schema(description = "BIP编号", example = "BIP编号")
    private String WW;
    //执行日期
    @Schema(description = "计划执行时间", example = "时间戳(毫秒)")
    private Long exec_date;

    @Schema(description = "钉钉接收人", example = "bip多个逗号隔开拼接")
    private String dingding_success_receiver ;

    @Schema(description = "系统名称", example = "NCC")
    private String system_name;

    @Schema(description = "数据库文件列表", required = true)
    private List<DbItem> db_0;

    @Schema(description = "表单校验状态", example = "true")
    private boolean schemaFormValid;

    @Schema(description = "外部参数（JSON字符串）", example = "{\"businessGroupId\":\"f4319fa3-e9a2-4ff6-92e1-21cce6f5420d\"}")
    private String externalParams;
}
