# 对接一问Agent标准API格式

#  API 标准对接格式

## 一、请求格式（Request）

### 1. 基础信息

*   **请求方法**：`POST`
    
*   **请求URL**：`请求URL,接入系统提供，例如：http://hr-dify-prod.bgyfw.com/v1/chat-messages`
    

### 2. 请求头（Headers）

```json
{
  "Authorization": "Bearer {api_key}",  // 接口授权令牌，必填（替换{api_key}为实际密钥）
  "Content-Type": "application/json"     // 数据格式，固定为JSON
}

```

### 3. 请求体（Body）

```json
{
  "inputs": {},  // 扩展参数，如上下文变量（可选，无特殊需求可留空）
  "query": "具体提问内容",  // 用户的问题/指令，必填（例如："查询我的年假余额"）
  "response_mode": "streaming",  // 响应模式：streaming（流式）/blocking（阻塞式），必填
  "conversation_id": "{对话ID}",  // 对话唯一标识，用于关联上下文（首次请求为空，后续复用返回的conversation_id）
  "user": "{用户标识}",  // 调用方的用户唯一标识（可选，用于区分不同用户）
  "files": [  // 附加文件（可选，如图片、文档等）
    {
      "type": "image",  // 文件类型（如image、document）
      "transfer_method": "remote_url",  // 传输方式（remote_url：远程URL；base64：Base64编码）
      "url": "https://example.com/file.png"  // 远程文件URL（transfer_method为remote_url时必填）
      // 若使用base64，需替换为"content": "base64编码内容"
    }
  ]
}

```

## 二、响应格式（Response）

### 1. 基础结构

```json
{
  "event": "message",  // 事件类型（固定为message，表示消息响应）
  "task_id": "xxx-xxx-xxx",  // 任务唯一标识（用于追踪请求）
  "id": "xxx-xxx-xxx",  // 消息ID（与message_id一致）
  "message_id": "xxx-xxx-xxx",  // 消息唯一标识
  "conversation_id": "xxx-xxx-xxx",  // 对话唯一标识（用于后续上下文关联）
  "mode": "advanced-chat",  // 对话模式（如chat/advanced-chat）
  "answer": "响应内容",  // 助手返回的回答文本
  "created_at": 1754908358  // 响应生成时间（Unix时间戳）
}

```

### 2. 字段说明

| 层级 | 字段 | 含义说明 |
| --- | --- | --- |
| 顶层 | `event` | 固定为`message`，标识这是一条消息响应 |
| 顶层 | `conversation_id` | 对话唯一ID，需保存用于后续上下文关联（如多轮对话） |
| 顶层 | `answer` | 助手返回的核心回答内容 |
| `metadata.usage` | `total_price` | 本次请求的总费用，单位由`currency`指定 |
| `metadata.usage` | `latency` | 响应延迟（秒），反映接口处理速度 |
| `metadata.retriever_resources` | `content` | 若启用知识库，返回用于生成回答的参考文档片段（可用于溯源） |

## 三、注意事项

1.  `**conversation_id**`：首次请求留空，后续多轮对话需传入上一次响应的`conversation_id`以保持上下文。
    
2.  `**response_mode**`：`streaming`适合实时展示（分块返回），`blocking`适合一次性获取完整结果。
    
3.  **鉴权**：`api_key`需妥善保管，若泄露需及时重置。
    
4.  **错误处理**：若请求格式错误或权限不足，接口会返回`4xx`状态码及错误信息，需在对接时处理。