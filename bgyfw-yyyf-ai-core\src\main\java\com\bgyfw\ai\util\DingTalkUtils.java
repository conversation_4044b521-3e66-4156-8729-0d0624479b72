package com.bgyfw.ai.util;

import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.base64url.internal.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 钉钉通知工具类
 * @date 2025/5/27 11:23
 */
@Slf4j
public class DingTalkUtils {

    private final static String SPLIT_STR = ";";

    public final static String MSG_TYPE_MARKDOWN = "markdown";
    public final static String MSG_TYPE_TEXT = "text";

    /**
     * 推送markdown消息
     *
     * @param token   token
     * @param secret  密钥
     * @param at      @
     * @param title   标题
     * @param message 消息
     */
    public static void sendMarkdown(String token, String secret, String at, String title, String message) {
        sendMessage(token, secret, at, MSG_TYPE_MARKDOWN, title, message);
    }

    /**
     * 推送text消息
     *
     * @param token   token
     * @param secret  密钥
     * @param at      @
     * @param title   标题
     * @param message 消息
     */
    public static void sendText(String token, String secret, String at, String title, String message) {
        sendMessage(token, secret, at, MSG_TYPE_TEXT, title, message);
    }

    /**
     * 调用钉钉通知
     *
     * @param token       token
     * @param secret      密钥
     * @param at          @
     * @param messageType 消息类型
     * @param title       标题
     * @param message     消息
     */
    public static void sendMessage(String token, String secret, String at, String messageType, String title, String message) {
        try {
            Long timestamp = System.currentTimeMillis();
            String sign = getSignature(timestamp, secret);

            //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();

            //定义内容
            if (StrUtil.equals(MSG_TYPE_MARKDOWN, messageType)) {
                OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
                markdown.setTitle(title);
                if (StrUtil.isNotEmpty(at)) {
                    StringBuilder messageBuilder = new StringBuilder(message);
                    messageBuilder.append("> **负责人**：");
                    String[] split = at.split(SPLIT_STR);

                    for (String s : split) {
                        messageBuilder.append("@").append(s).append(SPLIT_STR);
                    }
                    message = messageBuilder.toString();
                }
                markdown.setText(message);

                //设置消息类型
                req.setMsgtype(MSG_TYPE_MARKDOWN);
                req.setMarkdown(markdown);
            } else {
                OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
                text.setContent(message);
                //设置消息类型
                req.setMsgtype(MSG_TYPE_TEXT);
                req.setText(text);
            }

            if (StrUtil.isNotEmpty(at)) {
                req.setAt(getAt(at));
            }
            OapiRobotSendResponse rsp = client.execute(req, token);
            log.warn(rsp.getBody());
        } catch (ApiException | NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException ex) {
            log.error("发送钉钉通知时发生错误 ", ex);
        }
    }

    /**
     * 获取签名
     */
    private static String getSignature(Long timestamp, String secret) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        return URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
    }

    /**
     * 获取@用户
     */
    private static OapiRobotSendRequest.At getAt(String atStr) {
        String[] split = atStr.split(SPLIT_STR);
        //定义 @ 对象
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(Arrays.asList(split));
        return at;
    }

    public static void main(String[] args) {

        String message = "#### 自动导数处理失败，请人工进行处理 \n\n " +
                "> **流程单号**：A12345678 \n\n " +
                "> **失败原因**：" + "测试错误" + "\n\n ";
        DingTalkUtils.sendMarkdown("fcc43982b88e42af183d829d27d01ba5bc3b67893d8a8c7b151b7665d39f5798",
                "SEC2488ca3df4e71b90c909ff1d0c86711eaf2cc5da92905eaa5e8c8573b4aff856", "13160866595", "测试", message);

    }
}
