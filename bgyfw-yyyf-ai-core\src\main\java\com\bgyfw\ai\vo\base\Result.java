package com.bgyfw.ai.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应对象
 *
 * @Author: kinble
 * @Date: 2021-04-07 19:00
 */
@Data
@Schema(title = "响应对象", description = "响应对象")
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    private static final int SUCCESS = 200;

    /**
     * 失败
     */
    private static final int FAIL = 9999;

    @Schema(description = "响应代码")
    private Integer code;

    @Schema(description = "响应消息")
    private String msg;

    @Schema(description = "响应异常名称")
    private String exception;

    @Schema(description = "响应数据体")
    private T data;

    private Result() {
    }

    private static <T> Result<T> restResult(T data, int code, String msg, String exception) {
        Result<T> apiResult = new Result<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        apiResult.setException(exception);
        return apiResult;
    }

    public static <T> Result<T> ok() {
        return restResult(null, SUCCESS, null, null);
    }

    public static <T> Result<T> ok(T data) {
        return restResult(data, SUCCESS, null, null);
    }

    public static <T> Result<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg, null);
    }

    public static <T> Result<T> fail() {
        return restResult(null, FAIL, null, null);
    }

    public static <T> Result<T> fail(String msg) {
        return restResult(null, FAIL, msg, null);
    }

    public static <T> Result<T> fail(String msg, String exception) {
        return restResult(null, FAIL, msg, exception);
    }

    public static <T> Result<T> fail(T data) {
        return restResult(data, FAIL, null, null);
    }

    public static <T> Result<T> fail(T data, String msg) {
        return restResult(data, FAIL, msg, null);
    }

    public static <T> Result<T> fail(int code, String msg) {
        return restResult(null, code, msg, null);
    }

    public static <T> Result<T> fail(int code, String msg, String exception) {
        return restResult(null, code, msg, exception);
    }

}
