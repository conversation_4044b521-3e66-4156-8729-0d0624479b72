package com.bgyfw.ai.vo.operationPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录接口VO类
 *
 * <AUTHOR>
 * @date 2025/5/20 11:50
 */

/**
 * 登录请求参数VO
 */
@Data
public class LoginRequestVO {
    @Schema(description = "用户名", example = "ncc_xietong_user", required = true)
    private String username;

    @Schema(description = "密码", example = "Xv9@pRm2!Te6qA#ZNCC", required = true)
    private String password;

    @Schema(description = "是否加密", example = "false", defaultValue = "false")
    private String encrypted;

    @Schema(description = "登录类型", example = "Normal", required = true)
    private String loginType;
}
